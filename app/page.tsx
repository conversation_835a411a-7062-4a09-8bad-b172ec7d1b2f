'use client';

import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Send,
  Bot,
  User,
  CheckCircle,
  XCircle,
  Clock,
  Brain,
  ExternalLink,
  MessageSquare,
  Settings,
  Shield,
  LogOut,
  LogIn
} from 'lucide-react';
import { toast } from 'sonner';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { AuthService } from '@/lib/auth';

interface ChatMessage {
  id: string;
  text: string;
  sender: 'user' | 'bot';
  timestamp: Date;
  source?: string;
  sourceUrls?: { [title: string]: string };
  fromKnowledgeBase: boolean;
  embeddingType?: string;
}

interface RateLimit {
  count: number;
  resetTime: number;
}

const RATE_LIMIT_MAX = 20; // 20 messages per 5 minutes for public access
const RATE_LIMIT_WINDOW = 300000; // 5 minutes

export default function HomePage() {
  const [user, setUser] = useState<any>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputText, setInputText] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [rateLimit, setRateLimit] = useState<RateLimit>({ count: 0, resetTime: 0 });
  const [knowledgeStats, setKnowledgeStats] = useState<any>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const router = useRouter();

  useEffect(() => {
    // Initialize rate limit and load knowledge stats
    setRateLimit({ count: 0, resetTime: Date.now() + RATE_LIMIT_WINDOW });
    loadKnowledgeStats();

    // Check authentication status
    const checkAuth = async () => {
      const currentUser = await AuthService.getCurrentUser();
      setUser(currentUser);
    };
    checkAuth();

    // Listen for auth changes
    const { data: { subscription } } = AuthService.onAuthStateChange((user) => {
      setUser(user);
    });

    // Initialize with welcome message
    setMessages([{
      id: '1',
      text: 'Halo! Aku bisa bantu kamu jawab pertanyaan soal LPDP berdasarkan informasi yang aku punya',
      sender: 'bot',
      timestamp: new Date(),
      fromKnowledgeBase: true
    }]);

    return () => subscription.unsubscribe();
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const loadKnowledgeStats = async () => {
    try {
      // We'll make a simple request to get stats without authentication
      const response = await fetch('/api/chat-public', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message: '__GET_STATS__' }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.availableTopics || data.availableSources) {
          setKnowledgeStats({
            totalDocuments: data.availableSources?.length || 0,
            topics: data.availableTopics || [],
            sources: data.availableSources || []
          });
        }
      }
    } catch (error) {
      console.error('Error loading knowledge stats:', error);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const checkRateLimit = (): boolean => {
    const now = Date.now();

    if (now > rateLimit.resetTime) {
      setRateLimit({ count: 1, resetTime: now + RATE_LIMIT_WINDOW });
      return true;
    }

    if (rateLimit.count >= RATE_LIMIT_MAX) {
      toast.error('Rate limit exceeded. Please wait before sending another message.');
      return false;
    }

    setRateLimit(prev => ({ ...prev, count: prev.count + 1 }));
    return true;
  };

  const processMessage = async (text: string): Promise<{
    response: string;
    fromKnowledgeBase: boolean;
    source?: string;
    sourceUrls?: { [title: string]: string };
    embeddingType?: string
  }> => {
    try {
      const response = await fetch('/api/chat-public', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message: text }),
      });

      if (!response.ok) {
        throw new Error('Failed to process message');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error processing message:', error);
      return {
        response: 'I apologize, but I encountered an error processing your request. Please try again later.',
        fromKnowledgeBase: false
      };
    }
  };

  const handleSendMessage = async () => {
    if (!inputText.trim()) return;
    if (!checkRateLimit()) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      text: inputText,
      sender: 'user',
      timestamp: new Date(),
      fromKnowledgeBase: false
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsProcessing(true);

    try {
      const { response, fromKnowledgeBase, source, sourceUrls, embeddingType } = await processMessage(inputText);

      const botMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: response,
        sender: 'bot',
        timestamp: new Date(),
        source,
        sourceUrls,
        fromKnowledgeBase,
        embeddingType
      };

      setMessages(prev => [...prev, botMessage]);
    } catch (error) {
      console.error('Error:', error);
      toast.error('Failed to send message. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const getRemainingRequests = () => {
    const now = Date.now();
    if (now > rateLimit.resetTime) return RATE_LIMIT_MAX;
    return Math.max(0, RATE_LIMIT_MAX - rateLimit.count);
  };

  const getResetTimeMinutes = () => {
    const now = Date.now();
    const remaining = Math.max(0, rateLimit.resetTime - now);
    return Math.ceil(remaining / 60000);
  };

  const handleSourceClick = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  const handleSignOut = async () => {
    try {
      await AuthService.signOut();
      setUser(null);
      toast.success('Successfully signed out!');
    } catch (error) {
      console.error('Sign out error:', error);
      toast.error('Failed to sign out');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="container mx-auto px-4 py-6 max-w-4xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-500 rounded-lg">
              <Bot className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-slate-800">AI Knowledge Assistant</h1>
              <p className="text-slate-600 text-sm flex items-center gap-2">
                <Brain className="w-3 h-3" />
                Public Knowledge Base Chat
                {user && <span>• Welcome back, {user.email}</span>}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {user ? (
              <>
                <Badge variant="outline" className="flex items-center gap-1">
                  <Shield className="w-3 h-3" />
                  Authenticated
                </Badge>
                <Link href="/admin">
                  <Button variant="outline" size="sm">
                    <Settings className="w-4 h-4 mr-2" />
                    Admin Panel
                  </Button>
                </Link>
                <Link href="/dashboard">
                  <Button variant="outline" size="sm">
                    Dashboard
                  </Button>
                </Link>
                <Button variant="outline" size="sm" onClick={handleSignOut}>
                  <LogOut className="w-4 h-4 mr-2" />
                  Sign Out
                </Button>
              </>
            ) : (
              <Link href="/login">
                <Button variant="outline" size="sm">
                  <LogIn className="w-4 h-4 mr-2" />
                  Admin Login
                </Button>
              </Link>
            )}
          </div>
        </div>

        {/* Knowledge Base Status card removed */}

        {/* Rate Limit Status */}
        <Card className="mb-4 border-slate-200">
          <CardContent className="pt-4">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4 text-slate-500" />
                <span className="text-slate-600">
                  Requests remaining: <span className="font-medium">{getRemainingRequests()}</span>
                </span>
              </div>
              {getRemainingRequests() === 0 && (
                <span className="text-orange-600">
                  Resets in {getResetTimeMinutes()} minute{getResetTimeMinutes() !== 1 ? 's' : ''}
                </span>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Chat Interface */}
        <Card className="border-slate-200 shadow-lg">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2 text-lg">
              <MessageSquare className="w-5 h-5 text-blue-500" />
              Public Chat Session
            </CardTitle>
            <Separator />
          </CardHeader>

          <CardContent className="p-0">
            {/* Messages Container with Fixed Height and Proper Overflow */}
            <div className="h-[500px] flex flex-col">
              <ScrollArea className="flex-1 px-6">
                <div className="space-y-4 py-4">
                  {messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex gap-3 ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                    >
                      {message.sender === 'bot' && (
                        <div className="flex-shrink-0 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                          <Bot className="w-4 h-4 text-white" />
                        </div>
                      )}

                      <div className={`max-w-xs lg:max-w-md xl:max-w-lg ${message.sender === 'user' ? 'order-1' : ''}`}>
                        <div
                          className={`rounded-lg px-4 py-3 break-words ${
                            message.sender === 'user'
                              ? 'bg-blue-500 text-white'
                              : 'bg-white border border-slate-200'
                          }`}
                        >
                          <p className="text-sm leading-relaxed whitespace-pre-wrap break-words">{message.text}</p>

                          {message.sender === 'bot' && (
                            <div className="flex flex-col gap-2 mt-2 pt-2 border-t border-slate-100">
                              <div className="flex items-center gap-2">
                                <div className="flex items-center gap-1">
                                  {message.fromKnowledgeBase ? (
                                    <CheckCircle className="w-3 h-3 text-green-500" />
                                  ) : (
                                    <XCircle className="w-3 h-3 text-red-500" />
                                  )}
                                  <span className={`text-xs ${
                                    message.fromKnowledgeBase ? 'text-green-600' : 'text-red-600'
                                  }`}>
                                    {message.fromKnowledgeBase ? 'Knowledge Base' : 'No Source'}
                                  </span>
                                </div>
                              </div>

                              {/* Show source files with clickable URLs */}
                              {message.source && message.source.trim() && (
                                <div className="flex flex-wrap gap-1">
                                  {message.source.split(', ').map((sourceName, index) => {
                                    const sourceUrl = message.sourceUrls?.[sourceName];

                                    if (sourceUrl) {
                                      return (
                                        <Button
                                          key={index}
                                          variant="outline"
                                          size="sm"
                                          className="text-xs h-6 px-2 py-0 hover:bg-blue-50"
                                          onClick={() => handleSourceClick(sourceUrl)}
                                        >
                                          <ExternalLink className="w-3 h-3 mr-1" />
                                          {sourceName}
                                        </Button>
                                      );
                                    } else {
                                      return (
                                        <Badge key={index} variant="outline" className="text-xs">
                                          {sourceName}
                                        </Badge>
                                      );
                                    }
                                  })}
                                </div>
                              )}
                            </div>
                          )}
                        </div>

                        <p className="text-xs text-slate-500 mt-1 px-1">
                          {message.timestamp.toLocaleTimeString([], {
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </p>
                      </div>

                      {message.sender === 'user' && (
                        <div className="flex-shrink-0 w-8 h-8 bg-slate-500 rounded-full flex items-center justify-center order-2">
                          <User className="w-4 h-4 text-white" />
                        </div>
                      )}
                    </div>
                  ))}

                  {isProcessing && (
                    <div className="flex gap-3 justify-start">
                      <div className="flex-shrink-0 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                        <Bot className="w-4 h-4 text-white" />
                      </div>
                      <div className="bg-white border border-slate-200 rounded-lg px-4 py-3">
                        <div className="flex items-center gap-2">
                          <div className="flex gap-1">
                            <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce"></div>
                            <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                            <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                          </div>
                          <span className="text-sm text-slate-500">Searching knowledge base...</span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
                <div ref={messagesEndRef} />
              </ScrollArea>
            </div>

            {/* Input Area */}
            <div className="border-t border-slate-200 p-4">
              <div className="flex gap-2">
                <Input
                  value={inputText}
                  onChange={(e) => setInputText(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Ask me anything about the knowledge base..."
                  disabled={isProcessing || getRemainingRequests() === 0}
                  className="flex-1"
                />
                <Button
                  onClick={handleSendMessage}
                  disabled={!inputText.trim() || isProcessing || getRemainingRequests() === 0}
                  size="icon"
                >
                  <Send className="w-4 h-4" />
                </Button>
              </div>

              <div className="flex items-center justify-between mt-2 text-xs text-slate-500">
                <span>Press Enter to send • Public access</span>
                <span>{getRemainingRequests()} requests remaining</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
