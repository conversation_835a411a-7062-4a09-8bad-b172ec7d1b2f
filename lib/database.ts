import { supabase, supabaseAdmin, Database, createAuthenticatedClient } from './supabase'
import { KnowledgeItem, ChatMessage } from './knowledge-base-types'

export type DbKnowledgeSource = Database['public']['Tables']['knowledge_sources']['Row']
export type DbChatSession = Database['public']['Tables']['chat_sessions']['Row']
export type DbChatMessage = Database['public']['Tables']['chat_messages']['Row']
export type DbDocumentEmbedding = Database['public']['Tables']['document_embeddings']['Row']

// Knowledge Sources Operations
export class KnowledgeSourcesDB {
  static async getAll(userId?: string): Promise<KnowledgeItem[]> {
    try {
      const client = userId ? supabase : supabaseAdmin
      
      // Build the base query
      let query = client
        .from('knowledge_sources')
        .select('*')
        .eq('status', 'active')
      
      // Add user filter if userId is provided
      if (userId) {
        query = query.eq('created_by', userId)
      }
      
      // Execute the query with ordering
      const { data, error } = await query.order('created_at', { ascending: false })

      if (error) throw error

      return data.map(this.mapToKnowledgeItem)
    } catch (error) {
      console.error('Error fetching knowledge sources:', error)
      throw new Error('Failed to fetch knowledge sources')
    }
  }

  static async create(item: Omit<KnowledgeItem, 'id' | 'addedAt'>, userId: string, accessToken?: string): Promise<KnowledgeItem> {
    try {
      console.log('🔑 Creating knowledge source with user ID:', userId);
      console.log('🔍 Access token provided:', !!accessToken);

      // Use authenticated client if token is provided, otherwise use admin client
      const client = accessToken ? createAuthenticatedClient(accessToken) : supabaseAdmin;

      const insertData = {
        title: item.title,
        type: item.type,
        source: item.source,
        source_url: item.sourceUrl || null,
        content: item.content,
        topics: item.topics,
        status: item.status || 'active',
        file_path: null,
        file_size: item.fileSize || null,
        file_name: item.fileName || null,
        created_by: userId
      };

      console.log('📝 Insert data:', JSON.stringify(insertData, null, 2));

      const { data, error } = await client
        .from('knowledge_sources')
        .insert(insertData)
        .select()
        .single()

      if (error) {
        console.error('❌ Supabase insert error:', error);
        throw error;
      }

      console.log('✅ Successfully created knowledge source:', data.id);
      return this.mapToKnowledgeItem(data)
    } catch (error: any) {
      const errorDetails = {
        error: String(error),
        stack: error?.stack,
        code: error?.code,
        details: error?.details,
        hint: error?.hint,
        message: error?.message,
        name: error?.name
      };
      console.error('❌ Error creating knowledge source:', errorDetails);
      throw new Error(`Failed to create knowledge source: ${error?.message || 'Unknown error'}`);
    }
  }

  static async update(id: string, updates: Partial<KnowledgeItem>, userId: string): Promise<KnowledgeItem> {
    try {
      const { data, error } = await supabase
        .from('knowledge_sources')
        .update({
          title: updates.title,
          source_url: updates.sourceUrl || null,
          content: updates.content,
          topics: updates.topics,
          status: updates.status,
          file_size: updates.fileSize || null,
          file_name: updates.fileName || null
        })
        .eq('id', id)
        .eq('created_by', userId)
        .select()
        .single()

      if (error) throw error

      return this.mapToKnowledgeItem(data)
    } catch (error) {
      console.error('Error updating knowledge source:', error)
      throw new Error('Failed to update knowledge source')
    }
  }

  static async delete(id: string, userId: string): Promise<void> {
    try {
      console.log(`🗑️ Attempting to delete knowledge source: ${id} for user: ${userId}`);

      // First, verify the record exists and belongs to the user
      const existingRecord = await this.getById(id, userId);
      if (!existingRecord) {
        console.log(`⚠️ Knowledge source ${id} not found or doesn't belong to user ${userId}`);
        throw new Error('Knowledge source not found or access denied');
      }

      console.log(`✅ Found knowledge source to delete: ${existingRecord.title}`);

      // Perform the deletion
      const { data, error, count } = await supabase
        .from('knowledge_sources')
        .delete()
        .eq('id', id)
        .eq('created_by', userId)
        .select(); // This ensures we get the deleted record back

      if (error) {
        console.error('❌ Supabase delete error:', error);
        throw error;
      }

      // Verify deletion was successful
      if (!data || data.length === 0) {
        console.error('❌ Delete operation returned no data - deletion may have failed');
        throw new Error('Delete operation failed - no records were deleted');
      }

      console.log(`✅ Successfully deleted knowledge source: ${id}`, data);
    } catch (error) {
      console.error('Error deleting knowledge source:', error)
      throw new Error(`Failed to delete knowledge source: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  static async getById(id: string, userId?: string): Promise<KnowledgeItem | null> {
    try {
      const client = userId ? supabase : supabaseAdmin
      
      // Build the query with proper TypeScript type safety
      let query = client
        .from('knowledge_sources')
        .select('*')
        .eq('id', id)
      
      // Chain the condition if userId is provided
      if (userId) {
        query = query.eq('created_by', userId)
      }
      
      // Execute the query with single() at the end
      const { data, error } = await query.single()

      if (error) {
        if (error.code === 'PGRST116') return null // Not found
        throw error
      }

      return this.mapToKnowledgeItem(data)
    } catch (error) {
      console.error('Error fetching knowledge source by ID:', error)
      throw new Error('Failed to fetch knowledge source')
    }
  }

  private static mapToKnowledgeItem(dbItem: DbKnowledgeSource): KnowledgeItem {
    return {
      id: dbItem.id,
      title: dbItem.title,
      type: dbItem.type,
      source: dbItem.source,
      sourceUrl: dbItem.source_url || undefined,
      content: dbItem.content,
      topics: dbItem.topics,
      status: dbItem.status,
      fileSize: dbItem.file_size || undefined,
      fileName: dbItem.file_name || undefined,
      addedAt: new Date(dbItem.created_at)
    }
  }
}

// Document Embeddings Operations
export class DocumentEmbeddingsDB {
  // Helper function to validate UUIDs (accepts all UUID versions)
  private static isValidUUID(uuid: string | undefined): boolean {
    return typeof uuid === 'string' && /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(uuid);
  }

  static async create(sourceId: string, contentChunk: string, embedding: number[], chunkIndex: number): Promise<void> {
    try {
      // Validate sourceId before database insertion
      if (!this.isValidUUID(sourceId)) {
        const error = new Error(`Invalid UUID format for sourceId: ${sourceId}`);
        console.error('❌ UUID Validation Error:', {
          sourceId,
          contentPreview: contentChunk?.substring(0, 50) + '...',
          chunkIndex,
          timestamp: new Date().toISOString(),
          stackTrace: error.stack
        });
        throw error;
      }

      const { error } = await supabaseAdmin
        .from('document_embeddings')
        .insert({
          source_id: sourceId,
          content_chunk: contentChunk,
          embedding,
          chunk_index: chunkIndex
        })

      if (error) throw error

      console.log(`✅ Successfully created embedding for sourceId: ${sourceId}, chunk: ${chunkIndex}`);
    } catch (error) {
      console.error('Error creating document embedding:', error)
      throw new Error('Failed to create document embedding')
    }
  }

  static async deleteBySourceId(sourceId: string): Promise<void> {
    try {
      // Validate sourceId before deletion
      if (!this.isValidUUID(sourceId)) {
        console.error('❌ Invalid UUID provided to deleteBySourceId:', sourceId);
        throw new Error(`Invalid UUID format for sourceId: ${sourceId}`);
      }

      console.log(`🗑️ Attempting to delete embeddings for sourceId: ${sourceId}`);

      // First, check how many embeddings exist for this source
      const existingEmbeddings = await this.getEmbeddingsBySourceId(sourceId);
      console.log(`📊 Found ${existingEmbeddings.length} embeddings to delete for sourceId: ${sourceId}`);

      // Perform the deletion with select to get deleted records back
      const { data, error } = await supabaseAdmin
        .from('document_embeddings')
        .delete()
        .eq('source_id', sourceId)
        .select('id'); // Get back the IDs of deleted records

      if (error) {
        console.error('❌ Supabase delete embeddings error:', error);
        throw error;
      }

      const deletedCount = data ? data.length : 0;
      console.log(`✅ Successfully deleted ${deletedCount} embeddings for sourceId: ${sourceId}`);

      // Verify all embeddings were deleted
      if (existingEmbeddings.length > 0 && deletedCount === 0) {
        console.error('❌ No embeddings were deleted despite existing records');
        throw new Error('Failed to delete embeddings - no records were removed');
      }
    } catch (error) {
      console.error('Error deleting document embeddings:', error)
      throw new Error(`Failed to delete document embeddings: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  static async deleteInvalidEmbeddings(): Promise<{ deletedCount: number; invalidSourceIds: string[] }> {
    try {
      console.log('🧹 Starting cleanup of embeddings with invalid sourceIds...');

      // Get all embeddings to check for invalid UUIDs
      const allEmbeddings = await this.getAllEmbeddings(10000); // Get a large batch

      // Find embeddings with invalid sourceIds
      const invalidEmbeddings = allEmbeddings.filter(e => !this.isValidUUID(e.sourceId));

      if (invalidEmbeddings.length === 0) {
        console.log('✅ No invalid embeddings found');
        return { deletedCount: 0, invalidSourceIds: [] };
      }

      console.log(`⚠️ Found ${invalidEmbeddings.length} embeddings with invalid sourceIds`);

      const invalidSourceIds = [...new Set(invalidEmbeddings.map(e => e.sourceId))];
      const embeddingIds = invalidEmbeddings.map(e => e.id);

      // Delete embeddings with invalid sourceIds
      const { error } = await supabaseAdmin
        .from('document_embeddings')
        .delete()
        .in('id', embeddingIds)

      if (error) throw error

      console.log(`✅ Successfully deleted ${invalidEmbeddings.length} invalid embeddings`);
      console.log('🗑️ Deleted invalid sourceIds:', invalidSourceIds);

      return {
        deletedCount: invalidEmbeddings.length,
        invalidSourceIds
      };
    } catch (error) {
      console.error('Error cleaning up invalid embeddings:', error)
      throw new Error('Failed to clean up invalid embeddings')
    }
  }

  static async searchSimilar(queryEmbedding: number[], limit: number = 5, threshold: number = 0.1): Promise<{
    id: string
    sourceId: string
    content: string
    similarity: number
    chunkIndex: number
  }[]> {
    try {
      // Use RPC function for vector similarity search
      const { data, error } = await supabaseAdmin.rpc('search_embeddings', {
        query_embedding: queryEmbedding,
        match_threshold: threshold,
        match_count: limit
      })

      if (error) throw error

      return data || []
    } catch (error) {
      console.error('Error searching similar embeddings:', error)
      return []
    }
  }

  static async getAllEmbeddings(limit: number = 1000): Promise<{
    id: string
    sourceId: string
    content: string
    chunkIndex: number
    createdAt: string
  }[]> {
    try {
      const { data, error } = await supabaseAdmin
        .from('document_embeddings')
        .select('id, source_id, content_chunk, chunk_index, created_at')
        .order('created_at', { ascending: false })
        .limit(limit)

      if (error) throw error

      return (data || []).map(row => ({
        id: row.id,
        sourceId: row.source_id,
        content: row.content_chunk,
        chunkIndex: row.chunk_index,
        createdAt: row.created_at
      }))
    } catch (error) {
      console.error('Error fetching all embeddings:', error)
      return []
    }
  }

  static async getEmbeddingsBySourceId(sourceId: string): Promise<{
    id: string
    content: string
    chunkIndex: number
    createdAt: string
  }[]> {
    try {
      // Validate sourceId before querying
      if (!this.isValidUUID(sourceId)) {
        console.error('❌ Invalid UUID provided to getEmbeddingsBySourceId:', sourceId);
        return [];
      }

      const { data, error } = await supabaseAdmin
        .from('document_embeddings')
        .select('id, content_chunk, chunk_index, created_at')
        .eq('source_id', sourceId)
        .order('chunk_index', { ascending: true })

      if (error) throw error

      return (data || []).map(row => ({
        id: row.id,
        content: row.content_chunk,
        chunkIndex: row.chunk_index,
        createdAt: row.created_at
      }))
    } catch (error) {
      console.error('Error fetching embeddings by source ID:', error)
      return []
    }
  }
}

// Chat Sessions Operations
export class ChatSessionsDB {
  static async getAll(userId: string): Promise<DbChatSession[]> {
    try {
      const { data, error } = await supabase
        .from('chat_sessions')
        .select('*')
        .eq('user_id', userId)
        .order('updated_at', { ascending: false })

      if (error) throw error

      return data || []
    } catch (error) {
      console.error('Error fetching chat sessions:', error)
      throw new Error('Failed to fetch chat sessions')
    }
  }

  static async create(userId: string, title?: string): Promise<DbChatSession> {
    try {
      const { data, error } = await supabase
        .from('chat_sessions')
        .insert({
          user_id: userId,
          title: title || null
        })
        .select()
        .single()

      if (error) throw error

      return data
    } catch (error) {
      console.error('Error creating chat session:', error)
      throw new Error('Failed to create chat session')
    }
  }

  static async update(id: string, updates: { title?: string }, userId: string): Promise<DbChatSession> {
    try {
      const { data, error } = await supabase
        .from('chat_sessions')
        .update(updates)
        .eq('id', id)
        .eq('user_id', userId)
        .select()
        .single()

      if (error) throw error

      return data
    } catch (error) {
      console.error('Error updating chat session:', error)
      throw new Error('Failed to update chat session')
    }
  }

  static async delete(id: string, userId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('chat_sessions')
        .delete()
        .eq('id', id)
        .eq('user_id', userId)

      if (error) throw error
    } catch (error) {
      console.error('Error deleting chat session:', error)
      throw new Error('Failed to delete chat session')
    }
  }
}

// Chat Messages Operations
export class ChatMessagesDB {
  static async getBySessionId(sessionId: string, userId: string): Promise<ChatMessage[]> {
    try {
      // Verify session belongs to user
      const { data: session } = await supabase
        .from('chat_sessions')
        .select('id')
        .eq('id', sessionId)
        .eq('user_id', userId)
        .single()

      if (!session) {
        throw new Error('Session not found or access denied')
      }

      const { data, error } = await supabase
        .from('chat_messages')
        .select('*')
        .eq('session_id', sessionId)
        .order('created_at', { ascending: true })

      if (error) throw error

      return (data || []).map(this.mapToChatMessage)
    } catch (error) {
      console.error('Error fetching chat messages:', error)
      throw new Error('Failed to fetch chat messages')
    }
  }

  static async create(message: Omit<ChatMessage, 'id' | 'timestamp'>, sessionId: string): Promise<ChatMessage> {
    try {
      const { data, error } = await supabase
        .from('chat_messages')
        .insert({
          session_id: sessionId,
          content: message.text,
          role: message.sender === 'user' ? 'user' : 'assistant',
          sources: message.source ? message.source.split(', ') : null,
          source_urls: message.sourceUrls || null,
          from_knowledge_base: message.fromKnowledgeBase,
          embedding_type: message.embeddingType || null
        })
        .select()
        .single()

      if (error) throw error

      return this.mapToChatMessage(data)
    } catch (error) {
      console.error('Error creating chat message:', error)
      throw new Error('Failed to create chat message')
    }
  }

  private static mapToChatMessage(dbMessage: DbChatMessage): ChatMessage {
    return {
      id: dbMessage.id,
      text: dbMessage.content,
      sender: dbMessage.role === 'user' ? 'user' : 'bot',
      timestamp: new Date(dbMessage.created_at),
      source: dbMessage.sources?.join(', '),
      sourceUrls: dbMessage.source_urls as { [title: string]: string } | undefined,
      fromKnowledgeBase: dbMessage.from_knowledge_base,
      embeddingType: dbMessage.embedding_type || undefined
    }
  }
}

// Vector Search RPC Function (to be added to Supabase)
export const vectorSearchRPC = `
CREATE OR REPLACE FUNCTION search_embeddings(
  query_embedding vector(768),
  match_threshold float,
  match_count int
)
RETURNS TABLE (
  id uuid,
  source_id uuid,
  content text,
  similarity float,
  chunk_index int
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    de.id,
    de.source_id,
    de.content_chunk as content,
    1 - (de.embedding <=> query_embedding) as similarity,
    de.chunk_index
  FROM document_embeddings de
  JOIN knowledge_sources ks ON de.source_id = ks.id
  WHERE ks.status = 'active'
    AND 1 - (de.embedding <=> query_embedding) > match_threshold
  ORDER BY de.embedding <=> query_embedding
  LIMIT match_count;
END;
$$;
`;